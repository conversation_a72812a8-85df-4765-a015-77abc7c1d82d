// Type definitions for chat API
export interface ChatHistoryMessage {
  role: string;
  content: string;
}

export interface ChatRequest {
  message: string;
  chat_history?: ChatHistoryMessage[];
  use_agent?: boolean;
}

export interface ChatResponse {
  message: string;
  sources?: unknown[];
  metadata?: Record<string, unknown>;
}

// Base API URL with environment variable support
const API_URL = `${import.meta.env.VITE_API_URL || process.env.REACT_APP_API_URL || 'http://localhost:8000'}/api`;

// Helper function for API requests
const apiRequest = async (url: string, options: RequestInit = {}) => {
  const defaultHeaders = {
    'Content-Type': 'application/json',
    ...options.headers,
  };

  // Remove Content-Type for FormData
  if (options.body instanceof FormData) {
    delete defaultHeaders['Content-Type'];
  }

  const config: RequestInit = {
    credentials: 'include',
    ...options,
    headers: defaultHeaders,
  };

  try {
    const response = await fetch(url, config);

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText || response.statusText}`);
    }

    // Handle empty responses
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      return await response.json();
    } else {
      return await response.text();
    }
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
};

// Chat endpoints (updated to match backend implementation)
export const CHAT_ENDPOINTS = {
  SESSIONS: `${API_URL}/chat/sessions`,
  SESSION: (sessionId: string) => `${API_URL}/chat/sessions/${sessionId}`,
  SESSION_DOCUMENTS: (sessionId: string) => `${API_URL}/chat/sessions/${sessionId}/documents`,
  SESSION_DOCUMENT: (sessionId: string, documentId: string) => `${API_URL}/chat/sessions/${sessionId}/documents/${documentId}`,
  SESSION_DOCUMENT_DETAILS: (sessionId: string) => `${API_URL}/chat/sessions/${sessionId}/documents/details`,
  SESSION_MESSAGES: (sessionId: string) => `${API_URL}/chat/sessions/${sessionId}/messages`, // Updated endpoint
  SESSION_SUGGESTIONS: (sessionId: string) => `${API_URL}/chat/sessions/${sessionId}/suggestions`,
  // SSE streaming endpoint
  SSE_CHAT_STREAM: `${API_URL}/sse/chat/stream`,
  LEGACY_MESSAGE: `${API_URL}/chat/message`,
};

// Document endpoints
export const DOCUMENT_ENDPOINTS = {
  UPLOAD: `${API_URL}/documents/upload`,
  LIST: `${API_URL}/documents/list`,
  PREVIEW: (documentId: string) => `${API_URL}/documents/${documentId}/preview`,
  DELETE: (documentId: string) => `${API_URL}/documents/${documentId}`,
};

// Type definitions for API requests
export interface CreateSessionRequest {
  name: string;
  document_ids?: string[];
}

export interface UpdateSessionRequest {
  name: string;
}

export interface AddDocumentsToSessionRequest {
  document_ids: string[];
}

// Legacy format for backward compatibility
export interface LegacySendMessageRequest {
  message: string;
  use_agent?: boolean;
  document_ids?: string[];
}

// Chat API functions
export const chatApi = {
  // Session APIs
  createSession: async (data: CreateSessionRequest) => {
    return apiRequest(CHAT_ENDPOINTS.SESSIONS, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  listSessions: async () => {
    return apiRequest(CHAT_ENDPOINTS.SESSIONS);
  },

  getSession: async (sessionId: string) => {
    return apiRequest(CHAT_ENDPOINTS.SESSION(sessionId));
  },

  updateSession: async (sessionId: string, data: UpdateSessionRequest) => {
    return apiRequest(CHAT_ENDPOINTS.SESSION(sessionId), {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  },

  deleteSession: async (sessionId: string) => {
    return apiRequest(CHAT_ENDPOINTS.SESSION(sessionId), {
      method: 'DELETE',
    });
  },

  // Document APIs
  listSessionDocuments: async (sessionId: string) => {
    return apiRequest(CHAT_ENDPOINTS.SESSION_DOCUMENTS(sessionId));
  },

  getSessionDocumentDetails: async (sessionId: string) => {
    return apiRequest(CHAT_ENDPOINTS.SESSION_DOCUMENT_DETAILS(sessionId));
  },

  addDocumentsToSession: async (sessionId: string, data: AddDocumentsToSessionRequest) => {
    return apiRequest(CHAT_ENDPOINTS.SESSION_DOCUMENTS(sessionId), {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  },

  removeDocumentFromSession: async (sessionId: string, documentId: string) => {
    return apiRequest(CHAT_ENDPOINTS.SESSION_DOCUMENT(sessionId, documentId), {
      method: 'DELETE',
    });
  },

  // Message APIs
  getMessages: async (sessionId: string) => {
    return apiRequest(CHAT_ENDPOINTS.SESSION_MESSAGES(sessionId));
  },

  sendMessage: async (sessionId: string, request: ChatRequest): Promise<{ data: ChatResponse }> => {
    return apiRequest(CHAT_ENDPOINTS.SESSION_MESSAGES(sessionId), {
      method: 'POST',
      body: JSON.stringify(request),
    });
  },

  getSuggestions: async (sessionId: string) => {
    return apiRequest(CHAT_ENDPOINTS.SESSION_SUGGESTIONS(sessionId));
  },

  // Legacy message API
  sendLegacyMessage: async (data: LegacySendMessageRequest) => {
    return apiRequest(CHAT_ENDPOINTS.LEGACY_MESSAGE, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  // SSE streaming message API
  sendSSEMessage: async (sessionId: string, message: string, useAgent: boolean = false) => {
    return fetch(CHAT_ENDPOINTS.SSE_CHAT_STREAM, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({
        message,
        session_id: sessionId,
        use_agent: useAgent,
      }),
    });
  },
};

// Document API functions for chat
export const chatDocumentApi = {
  uploadDocumentToSession: async (file: File, sessionId?: string) => {
    const formData = new FormData();
    formData.append('file', file);

    const url = sessionId
      ? `${DOCUMENT_ENDPOINTS.UPLOAD}?session_id=${sessionId}`
      : DOCUMENT_ENDPOINTS.UPLOAD;

    return apiRequest(url, {
      method: 'POST',
      body: formData,
      headers: {}, // Let the browser set the Content-Type for FormData
    });
  },

  listAllDocuments: async () => {
    return apiRequest(DOCUMENT_ENDPOINTS.LIST);
  },

  getDocumentPreview: async (documentId: string) => {
    return apiRequest(DOCUMENT_ENDPOINTS.PREVIEW(documentId));
  },

  deleteDocument: async (documentId: string) => {
    return apiRequest(DOCUMENT_ENDPOINTS.DELETE(documentId), {
      method: 'DELETE',
    });
  },
};

// Utility functions
export const chatApiUtils = {
  // Check if API is available
  healthCheck: async () => {
    try {
      const response = await fetch(`${API_URL.replace('/api', '')}/health`);
      return response.ok;
    } catch (error) {
      console.error('Health check failed:', error);
      return false;
    }
  },

  // Get WebSocket status
  getWebSocketStatus: async () => {
    try {
      return await apiRequest(`${API_URL}/websocket/status`);
    } catch (error) {
      console.error('WebSocket status check failed:', error);
      return { status: 'unavailable' };
    }
  },

  // Test WebSocket functionality
  testWebSocket: {
    triggerFileUpdate: async (fileId: string, status: string, progress?: number) => {
      return apiRequest(`${API_URL}/websocket/test/file-update`, {
        method: 'POST',
        body: JSON.stringify({
          file_id: fileId,
          status,
          progress,
          metadata: { test: true }
        }),
      });
    },

    triggerChatChunk: async (chatSessionId: string, chunk: string, isFinal = false) => {
      return apiRequest(`${API_URL}/websocket/test/chat-chunk`, {
        method: 'POST',
        body: JSON.stringify({
          chat_session_id: chatSessionId,
          chunk,
          is_final: isFinal,
          metadata: { test: true }
        }),
      });
    },
  },
};

// Export all APIs
export default {
  chat: chatApi,
  document: chatDocumentApi,
  utils: chatApiUtils,
};
