/**
 * WebSocket utility functions for connection management and error handling
 */

export interface WebSocketConfig {
  url: string;
  userId: string;
  maxReconnectAttempts?: number;
  reconnectDelay?: number;
  timeout?: number;
}

export interface ConnectionState {
  isConnected: boolean;
  isConnecting: boolean;
  reconnectAttempts: number;
  lastError: string | null;
}

/**
 * Calculate exponential backoff delay for reconnection attempts
 */
export const calculateReconnectDelay = (attempt: number, baseDelay: number = 1000): number => {
  return Math.min(baseDelay * Math.pow(2, attempt), 30000); // Max 30 seconds
};

/**
 * Validate WebSocket URL format
 */
export const validateWebSocketUrl = (url: string): boolean => {
  try {
    const parsedUrl = new URL(url);
    return parsedUrl.protocol === 'ws:' || parsedUrl.protocol === 'wss:';
  } catch {
    return false;
  }
};

/**
 * Get WebSocket URL from environment variables with fallback
 */
export const getWebSocketUrl = (): string => {
  const wsUrl = import.meta.env.VITE_WEBSOCKET_URL ||
                process.env.REACT_APP_WEBSOCKET_URL;

  if (wsUrl && validateWebSocketUrl(wsUrl)) {
    return wsUrl;
  }

  // Fallback to API URL and convert to WebSocket URL
  const apiUrl = import.meta.env.VITE_API_URL ||
                 process.env.REACT_APP_API_URL ||
                 'http://localhost:8000';

  // Convert HTTP(S) to WS(S)
  return apiUrl.replace(/^https?:/, apiUrl.startsWith('https:') ? 'wss:' : 'ws:');
};

/**
 * Format connection error messages for user display
 */
export const formatConnectionError = (error: any): string => {
  if (typeof error === 'string') return error;

  if (error?.message) {
    // Common error message formatting
    if (error.message.includes('ECONNREFUSED')) {
      return 'Connection refused - Server may be down';
    }
    if (error.message.includes('timeout')) {
      return 'Connection timeout - Please check your network';
    }
    if (error.message.includes('ENOTFOUND')) {
      return 'Server not found - Please check the URL';
    }
    return error.message;
  }

  return 'Unknown connection error';
};

/**
 * Check if error is recoverable (should attempt reconnection)
 */
export const isRecoverableError = (error: any): boolean => {
  if (typeof error === 'string') {
    return !error.includes('unauthorized') && !error.includes('forbidden');
  }

  if (error?.message) {
    const message = error.message.toLowerCase();
    return !message.includes('unauthorized') &&
           !message.includes('forbidden') &&
           !message.includes('invalid token');
  }

  return true;
};

/**
 * WebSocket event names used by the application
 */
export const WEBSOCKET_EVENTS = {
  // Connection events
  CONNECT: 'connect',
  DISCONNECT: 'disconnect',
  CONNECT_ERROR: 'connect_error',
  ERROR: 'error',

  // Room management
  JOIN_FILE_ROOM: 'join_file_room',
  LEAVE_FILE_ROOM: 'leave_file_room',
  JOIN_CHAT_ROOM: 'join_chat_room',
  LEAVE_CHAT_ROOM: 'leave_chat_room',

  // File processing events
  FILE_STATUS_UPDATE: 'file_status_update',
  PROCESSING_PROGRESS: 'processing_progress',

  // Chat events
  CHAT_RESPONSE_CHUNK: 'chat_response_chunk',
  CHAT_ERROR: 'chat_error',

  // System events
  SYSTEM_MESSAGE: 'system_message',
  RATE_LIMIT: 'rate_limit'
} as const;

/**
 * Default WebSocket configuration
 */
export const DEFAULT_WEBSOCKET_CONFIG = {
  maxReconnectAttempts: 5,
  reconnectDelay: 1000,
  timeout: 20000,
  transports: ['websocket', 'polling'] as string[],
  forceNew: true
};

/**
 * Create WebSocket connection options
 */
export const createWebSocketOptions = (userId: string, config?: Partial<typeof DEFAULT_WEBSOCKET_CONFIG>) => {
  return {
    auth: { user_id: userId },
    ...DEFAULT_WEBSOCKET_CONFIG,
    ...config
  };
};

/**
 * Log WebSocket events for debugging
 */
export const logWebSocketEvent = (event: string, data?: any) => {
  if (process.env.NODE_ENV === 'development') {
    console.log(`[WebSocket] ${event}:`, data);
  }
};

/**
 * Sanitize data for logging (remove sensitive information)
 */
export const sanitizeLogData = (data: any): any => {
  if (!data || typeof data !== 'object') return data;

  const sanitized = { ...data };

  // Remove sensitive fields
  const sensitiveFields = ['token', 'password', 'auth', 'authorization', 'secret'];
  sensitiveFields.forEach(field => {
    if (field in sanitized) {
      sanitized[field] = '[REDACTED]';
    }
  });

  return sanitized;
};
