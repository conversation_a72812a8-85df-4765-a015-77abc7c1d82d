import React, { useState, useRef, useEffect } from 'react';
import { useSSEChat } from '../hooks/useSSEChat';
import { useAppSelector, useAppDispatch } from '@/app/hooks';
import { 
  selectMessages, 
  selectUseAgent, 
  selectIsStreaming, 
  selectCurrentResponse,
  addMessage,
  setUseAgent,
  startStreaming,
  updateStream,
  completeStream,
  stopStreaming
} from '@/features/chat/chatSlice';
import { ChatMessage } from '@/types/chat';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import MarkdownRenderer from './MarkdownRenderer';

interface ChatWithSSEProps {
  sessionId: string;
}

export const ChatWithSSE: React.FC<ChatWithSSEProps> = ({ sessionId }) => {
  const [inputMessage, setInputMessage] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const dispatch = useAppDispatch();

  // Redux selectors
  const reduxMessages = useAppSelector(selectMessages);
  const useAgent = useAppSelector(selectUseAgent);
  const isStreaming = useAppSelector(selectIsStreaming);
  const currentResponse = useAppSelector(selectCurrentResponse);

  // SSE Chat hook
  const { sendMessage: sendSSEMessage } = useSSEChat({
    sessionId,
    onMessage: (message) => {
      dispatch(completeStream(message));
    },
    onError: (error) => {
      console.error('Chat error:', error);
      dispatch(stopStreaming());
      toast.error(`Error: ${error}`);
      
      // Add error message to chat
      const errorMessage: ChatMessage = {
        id: `error-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        role: 'assistant',
        content: `Error: ${error}`,
        timestamp: new Date().toISOString(),
      };
      dispatch(addMessage(errorMessage));
    },
  });

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isStreaming) return;

    // Add user message to Redux
    const userMessage: ChatMessage = {
      id: `user-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      role: 'user',
      content: inputMessage,
      timestamp: new Date().toISOString(),
    };

    dispatch(addMessage(userMessage));
    const messageToSend = inputMessage;
    setInputMessage('');

    // Start streaming state
    dispatch(startStreaming());

    try {
      // Send via SSE
      await sendSSEMessage(messageToSend, useAgent);
    } catch (error) {
      console.error('Error sending message:', error);
      dispatch(stopStreaming());
      toast.error('Failed to send message');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleToggleAgent = () => {
    if (!isStreaming) {
      dispatch(setUseAgent(!useAgent));
    }
  };

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [reduxMessages, currentResponse]);

  return (
    <div className="flex flex-col h-full">
      {/* Mode Toggle */}
      <div className="flex justify-between items-center p-4 border-b">
        <h2 className="text-lg font-semibold">Chat</h2>
        <Button
          onClick={handleToggleAgent}
          variant={useAgent ? "default" : "outline"}
          disabled={isStreaming}
          className={`transition-all duration-200 ${
            useAgent 
              ? 'bg-blue-600 hover:bg-blue-700 text-white' 
              : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
          }`}
        >
          <span className="mr-2">🤖</span>
          {useAgent ? 'Agent Mode' : 'RAG Mode'}
        </Button>
      </div>

      {/* Messages Container */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {reduxMessages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[80%] rounded-lg p-3 ${
                message.role === 'user'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-900'
              }`}
            >
              <div className="prose prose-sm max-w-none">
                {message.role === 'assistant' ? (
                  <MarkdownRenderer content={message.content} />
                ) : (
                  <p className="whitespace-pre-wrap">{message.content}</p>
                )}
              </div>
              <div className={`text-xs mt-2 ${
                message.role === 'user' ? 'text-blue-100' : 'text-gray-500'
              }`}>
                {new Date(message.timestamp).toLocaleTimeString()}
              </div>
            </div>
          </div>
        ))}

        {/* Streaming Response */}
        {isStreaming && currentResponse && (
          <div className="flex justify-start">
            <div className="max-w-[80%] rounded-lg p-3 bg-gray-100 text-gray-900">
              <div className="prose prose-sm max-w-none">
                <MarkdownRenderer content={currentResponse} />
              </div>
              <div className="flex items-center mt-2">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
                <span className="text-xs text-gray-500 ml-2">Streaming...</span>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="border-t p-4">
        <div className="flex space-x-2">
          <Textarea
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type your message..."
            disabled={isStreaming}
            rows={3}
            className="flex-1 resize-none"
          />
          <Button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || isStreaming}
            className="self-end"
          >
            {isStreaming ? 'Sending...' : 'Send'}
          </Button>
        </div>
      </div>
    </div>
  );
};
