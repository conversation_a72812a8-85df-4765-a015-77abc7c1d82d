import { useState, useRef, useCallback } from 'react';
import { useAppDispatch } from '@/app/hooks';
import { updateStream } from '@/features/chat/chatSlice';

interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  metadata?: any;
}

interface UseSSEChatProps {
  sessionId: string;
  onMessage?: (message: ChatMessage) => void;
  onError?: (error: string) => void;
}

export const useSSEChat = ({ sessionId, onMessage, onError }: UseSSEChatProps) => {
  const [isStreaming, setIsStreaming] = useState(false);
  const [currentResponse, setCurrentResponse] = useState('');
  const eventSourceRef = useRef<EventSource | null>(null);
  const dispatch = useAppDispatch();

  const sendMessage = useCallback(async (message: string, useAgent: boolean = false) => {
    if (isStreaming) {
      onError?.('Please wait for the current response to complete');
      return;
    }

    setIsStreaming(true);
    setCurrentResponse('');

    try {
      // First, send the message via SSE API
      const apiUrl = import.meta.env.VITE_API_URL || process.env.REACT_APP_API_URL || 'http://localhost:8000';
      const response = await fetch(`${apiUrl}/api/sse/chat/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Use cookies for authentication instead of Bearer token
        body: JSON.stringify({
          message,
          session_id: sessionId,
          use_agent: useAgent,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Create SSE connection for streaming response
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (!reader) {
        throw new Error('No response body reader available');
      }

      let buffer = '';
      let assistantMessage = '';

      while (true) {
        const { done, value } = await reader.read();

        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));

              switch (data.type) {
                case 'connected':
                  console.log('SSE connected');
                  break;

                case 'chunk':
                  assistantMessage += data.chunk;
                  setCurrentResponse(assistantMessage);
                  dispatch(updateStream(assistantMessage));
                  break;

                case 'complete':
                  const finalMessage: ChatMessage = {
                    id: Date.now().toString(),
                    role: 'assistant',
                    content: assistantMessage,
                    timestamp: new Date().toISOString(),
                    metadata: data.metadata,
                  };
                  onMessage?.(finalMessage);
                  setCurrentResponse('');
                  break;

                case 'error':
                  onError?.(data.error || 'An error occurred');
                  break;
              }
            } catch (e) {
              console.error('Error parsing SSE data:', e);
            }
          }
        }
      }
    } catch (error) {
      onError?.(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setIsStreaming(false);
    }
  }, [sessionId, isStreaming, onMessage, onError]);

  const stopStreaming = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    setIsStreaming(false);
  }, []);

  return {
    sendMessage,
    stopStreaming,
    isStreaming,
    currentResponse,
  };
};
